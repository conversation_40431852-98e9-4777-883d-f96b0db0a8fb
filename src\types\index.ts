// Electron API types
export interface ElectronAPI {
  storeApiKey: (apiKey: string) => Promise<{ success: boolean }>;
  getApiKey: () => Promise<{ apiKey: string | null }>;
  storeSelectedModel: (modelId: string) => Promise<{ success: boolean }>;
  getSelectedModel: () => Promise<{ modelId: string | null }>;
  showSaveDialog: () => Promise<{ canceled: boolean; filePath?: string }>;
  showOpenDialog: () => Promise<{ canceled: boolean; filePaths?: string[] }>;
  platform: string;
  isDev: () => Promise<boolean>;
}

// Gemini API types
export interface GeminiConfig {
  apiKey: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface EnhancementRequest {
  originalPrompt: string;
  mode: EnhancementMode;
  context?: string;
  style?: EnhancementStyle;
}

export interface EnhancementResponse {
  enhancedPrompt: string;
  suggestions?: string[];
  improvements?: string[];
}

// Model types for Gemini API
export interface GeminiModel {
  name: string;
  baseModelId: string;
  version: string;
  displayName: string;
  description: string;
  inputTokenLimit: number;
  outputTokenLimit: number;
  supportedGenerationMethods: string[];
  temperature?: number;
  maxTemperature?: number;
  topP?: number;
  topK?: number;
}

export interface ListModelsResponse {
  models: GeminiModel[];
  nextPageToken?: string;
}

// Enhancement modes
export type EnhancementMode = "quick" | "structured" | "template" | "batch";

export type EnhancementStyle =
  | "creative"
  | "concise"
  | "technical"
  | "detailed";

// Application state types
export interface AppState {
  originalPrompt: string;
  enhancedPrompt: string;
  isLoading: boolean;
  error: string | null;
  apiKey: string | null;
  currentMode: EnhancementMode;
  currentStyle: EnhancementStyle;
}

// History types
export interface PromptHistory {
  id: string;
  timestamp: Date;
  originalPrompt: string;
  enhancedPrompt: string;
  mode: EnhancementMode;
  style: EnhancementStyle;
}

// Global window interface extension
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
