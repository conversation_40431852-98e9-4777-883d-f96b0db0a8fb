import { useState, useEffect } from "react";
import { Editor } from "./components/Editor";
import { ApiKeyModal } from "./components/ApiKeyModal";
import { ModelSelector } from "./components/ModelSelector";
import { geminiService } from "./services/geminiService";
import { AppState, EnhancementMode, EnhancementStyle } from "./types";

function App() {
  const [state, setState] = useState<AppState>({
    originalPrompt: "",
    enhancedPrompt: "",
    isLoading: false,
    error: null,
    apiKey: null,
    currentMode: "quick",
    currentStyle: "detailed",
  });

  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string | null>(null);

  useEffect(() => {
    // Check if API key and selected model are stored
    if (window.electronAPI) {
      Promise.all([
        window.electronAPI.getApiKey(),
        window.electronAPI.getSelectedModel(),
        window.electronAPI.isDev(),
      ]).then(([{ apiKey }, { modelId }, isDevMode]) => {
        if (apiKey) {
          setState((prev) => ({ ...prev, apiKey }));
          geminiService.initialize({
            apiKey,
            model: modelId || undefined,
          });
          setSelectedModel(modelId);
        } else if (!isDevMode) {
          // Only show modal in production if API key is missing
          setShowApiKeyModal(true);
        }
      });
    }
  }, []);

  const handleApiKeySubmit = async (apiKey: string) => {
    try {
      geminiService.initialize({
        apiKey,
        model: selectedModel || undefined,
      });

      if (window.electronAPI) {
        await window.electronAPI.storeApiKey(apiKey);
      }

      setState((prev) => ({ ...prev, apiKey }));
      setShowApiKeyModal(false);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error:
          "Failed to initialize Gemini service. Please check your API key.",
      }));
    }
  };

  const handleModelSelect = async (modelId: string) => {
    try {
      // Update the service to use the new model
      geminiService.updateModel(modelId);

      // Store the selected model
      if (window.electronAPI) {
        await window.electronAPI.storeSelectedModel(modelId);
      }

      setSelectedModel(modelId);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: "Failed to update model selection.",
      }));
    }
  };

  const handleEnhancePrompt = async () => {
    if (!state.originalPrompt.trim()) {
      setState((prev) => ({
        ...prev,
        error: "Please enter a prompt to enhance.",
      }));
      return;
    }

    if (!geminiService.isInitialized()) {
      setShowApiKeyModal(true);
      return;
    }

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await geminiService.enhancePrompt({
        originalPrompt: state.originalPrompt,
        mode: state.currentMode,
        style: state.currentStyle,
      });

      setState((prev) => ({
        ...prev,
        enhancedPrompt: response.enhancedPrompt,
        isLoading: false,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error:
          error instanceof Error
            ? error.message
            : "An error occurred while enhancing the prompt.",
        isLoading: false,
      }));
    }
  };

  const handleModeChange = (mode: EnhancementMode) => {
    setState((prev) => ({ ...prev, currentMode: mode }));
  };

  const handleStyleChange = (style: EnhancementStyle) => {
    setState((prev) => ({ ...prev, currentStyle: style }));
  };

  const handlePromptChange = (prompt: string) => {
    setState((prev) => ({ ...prev, originalPrompt: prompt }));
  };

  const handleClearError = () => {
    setState((prev) => ({ ...prev, error: null }));
  };

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Prompt Enhancer</h1>
          <div className="flex items-center space-x-4">
            {/* Mode Selection */}
            <select
              value={state.currentMode}
              onChange={(e) =>
                handleModeChange(e.target.value as EnhancementMode)
              }
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="quick">Quick Enhancement</option>
              <option value="structured">Structured Mode</option>
              <option value="template">Template Mode</option>
            </select>

            {/* Style Selection */}
            <select
              value={state.currentStyle}
              onChange={(e) =>
                handleStyleChange(e.target.value as EnhancementStyle)
              }
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="detailed">Detailed</option>
              <option value="concise">Concise</option>
              <option value="creative">Creative</option>
              <option value="technical">Technical</option>
            </select>

            {/* API Key Button */}
            <button
              onClick={() => setShowApiKeyModal(true)}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              {state.apiKey ? "Update API Key" : "Set API Key"}
            </button>
          </div>
        </div>
      </header>

      {/* Error Banner */}
      {state.error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mx-6 mt-4 rounded">
          <div className="flex items-center justify-between">
            <p className="text-red-700">{state.error}</p>
            <button
              onClick={handleClearError}
              className="text-red-400 hover:text-red-600"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Model Selection */}
      {state.apiKey && (
        <div className="px-6 mt-4">
          <ModelSelector
            selectedModel={selectedModel}
            onModelSelect={handleModelSelect}
            disabled={state.isLoading}
          />
        </div>
      )}

      {/* Main Content */}
      <main className="flex-1 p-6">
        <Editor
          originalPrompt={state.originalPrompt}
          enhancedPrompt={state.enhancedPrompt}
          isLoading={state.isLoading}
          onPromptChange={handlePromptChange}
          onEnhance={handleEnhancePrompt}
        />
      </main>

      {/* API Key Modal */}
      {showApiKeyModal && (
        <ApiKeyModal
          onSubmit={handleApiKeySubmit}
          onClose={() => setShowApiKeyModal(false)}
        />
      )}
    </div>
  );
}

export default App;
